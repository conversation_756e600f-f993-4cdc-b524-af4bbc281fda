'use client'
import { Faculty } from "@/types/faculty.type";
import React from 'react';

import ArrowDownIcon from '@/assets/icons/arrow-down-icon.svg';
import {
    Accordion,
    AccordionContent,
    AccordionHeader,
    AccordionIcon,
    AccordionItem,
    AccordionTitleText,
    AccordionTrigger
} from "@/components/ui/Accordion/Accordion";
import { Text } from "@/components/ui/Text/Text";
import { LocaleEnum } from "@/enums/locale.enum";
import { useAppLanguage } from "@/hooks/common/useAppLanguage";
import { BodyPart } from "@/types/body-part.type";
import { LocalizeField } from "@/types/global.type";

type MedicalFacultyItemProps = {
    faculty: Faculty;
}

export const MedicalFacultyItem = ({ faculty }: MedicalFacultyItemProps) => {
    const { primaryLanguage, secondaryLanguage } = useAppLanguage()
    const name = (faculty?.name as unknown as LocalizeField<string>)

    return (
        <Accordion type="single" variant="unfilled">
            <AccordionItem value={`faculty-${faculty.id}`}>
                <AccordionHeader>
                    <AccordionTrigger >
                        <AccordionTitleText>
                            {name[primaryLanguage as LocaleEnum]} { }
                            ({name[secondaryLanguage as LocaleEnum]})
                        </AccordionTitleText>
                        <AccordionIcon>
                            <ArrowDownIcon className="size-4" />
                        </AccordionIcon>
                    </AccordionTrigger>
                </AccordionHeader>
                <AccordionContent>
                    {faculty?.bodyParts?.filter(bodyPart => (bodyPart as BodyPart)?.name).map((bodyPart) => {
                        const { name, id } = bodyPart as BodyPart
                        const localizedBodyPartName = name as unknown as LocalizeField<string>

                        return (
                            <Text key={id} size="body6" variant="default" style={{ marginBottom: 4 }}>
                                {localizedBodyPartName[primaryLanguage as LocaleEnum]} { }
                                ({localizedBodyPartName[secondaryLanguage as LocaleEnum]})
                            </Text>
                        )
                    })}
                    <Text size="body6" variant="default">
                        {faculty.description}
                    </Text>
                </AccordionContent>
            </AccordionItem>
        </Accordion>
    );
}

